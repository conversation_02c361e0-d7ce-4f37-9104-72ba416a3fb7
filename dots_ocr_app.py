#!/usr/bin/env python3
"""
Standalone Dots OCR Application
Based on the Hugging Face space: https://huggingface.co/spaces/MohamedRashad/Dots-OCR

This application uses ONLY Dots OCR for text extraction from images and PDFs.
No other OCR methods are used as fallback.
"""

import spaces
import json
import math
import os
import traceback
from io import BytesIO
from typing import Any, Dict, List, Optional, Tuple
import re

import fitz  # PyMuPDF
import gradio as gr
import requests
import torch
from huggingface_hub import snapshot_download
from PIL import Image, ImageDraw, ImageFont
from qwen_vl_utils import process_vision_info
from transformers import AutoModelForCausalLM, AutoProcessor

# Constants
MIN_PIXELS = 3136
MAX_PIXELS = 11289600
IMAGE_FACTOR = 28

# Prompts
LAYOUT_PROMPT = """Please output the layout information from the PDF image, including each layout element's bbox, its category, and the corresponding text content within the bbox.

1. Bbox format: [x1, y1, x2, y2]

2. Layout Categories: The possible categories are ['Caption', 'Footnote', 'Formula', 'List-item', 'Page-footer', 'Page-header', 'Picture', 'Section-header', 'Table', 'Text', 'Title'].

3. Text Extraction & Formatting Rules:
    - Picture: For the 'Picture' category, the text field should be omitted.
    - Formula: Format its text as LaTeX.
    - Table: Format its text as HTML.
    - All Others (Text, Title, etc.): Format their text as Markdown.

4. Constraints:
    - The output text must be the original text from the image, with no translation.
    - All layout elements must be sorted according to human reading order.

5. Final Output: The entire output must be a single JSON object.
"""

SIMPLE_OCR_PROMPT = """Extract all text content from this image and return it as plain text, maintaining the reading order."""

# Utility functions
def round_by_factor(number: int, factor: int) -> int:
    """Returns the closest integer to 'number' that is divisible by 'factor'."""
    return round(number / factor) * factor

def smart_resize(
    height: int,
    width: int,
    factor: int = 28,
    min_pixels: int = 3136,
    max_pixels: int = 11289600,
):
    """Rescales the image so that the following conditions are met:
    1. Both dimensions (height and width) are divisible by 'factor'.
    2. The total number of pixels is within the range ['min_pixels', 'max_pixels'].
    3. The aspect ratio of the image is maintained as closely as possible.
    """
    if max(height, width) / min(height, width) > 200:
        raise ValueError(
            f"absolute aspect ratio must be smaller than 200, got {max(height, width) / min(height, width)}"
        )
    h_bar = max(factor, round_by_factor(height, factor))
    w_bar = max(factor, round_by_factor(width, factor))
    
    if h_bar * w_bar > max_pixels:
        beta = math.sqrt((height * width) / max_pixels)
        h_bar = round_by_factor(height / beta, factor)
        w_bar = round_by_factor(width / beta, factor)
    elif h_bar * w_bar < min_pixels:
        beta = math.sqrt(min_pixels / (height * width))
        h_bar = round_by_factor(height * beta, factor)
        w_bar = round_by_factor(width * beta, factor)
    
    return h_bar, w_bar

def fetch_image(image_input, min_pixels: int = None, max_pixels: int = None):
    """Fetch and process an image"""
    if isinstance(image_input, str):
        if image_input.startswith(("http://", "https://")):
            response = requests.get(image_input)
            image = Image.open(BytesIO(response.content)).convert('RGB')
        else:
            image = Image.open(image_input).convert('RGB')
    elif isinstance(image_input, Image.Image):
        image = image_input.convert('RGB')
    else:
        raise ValueError(f"Invalid image input type: {type(image_input)}")
    
    if min_pixels is not None or max_pixels is not None:
        min_pixels = min_pixels or MIN_PIXELS
        max_pixels = max_pixels or MAX_PIXELS
        height, width = smart_resize(
            image.height,
            image.width,
            factor=IMAGE_FACTOR,
            min_pixels=min_pixels,
            max_pixels=max_pixels
        )
        image = image.resize((width, height), Image.LANCZOS)
    
    return image

def load_images_from_pdf(pdf_path: str) -> List[Image.Image]:
    """Load images from PDF file"""
    images = []
    try:
        pdf_document = fitz.open(pdf_path)
        for page_num in range(len(pdf_document)):
            page = pdf_document.load_page(page_num)
            # Convert page to image
            mat = fitz.Matrix(2.0, 2.0)  # Increase resolution
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("ppm")
            image = Image.open(BytesIO(img_data)).convert('RGB')
            images.append(image)
        pdf_document.close()
    except Exception as e:
        print(f"Error loading PDF: {e}")
        return []
    return images

class DotsOCRApp:
    """Standalone Dots OCR Application"""
    
    def __init__(self):
        self.model = None
        self.processor = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_id = "rednote-hilab/dots.ocr"
        self.model_path = "./models/dots-ocr-local"
        
        print(f"Initializing Dots OCR App on {self.device}")
        self._load_model()
    
    def _load_model(self):
        """Load the Dots OCR model"""
        try:
            print("Downloading Dots OCR model...")
            snapshot_download(
                repo_id=self.model_id,
                local_dir=self.model_path,
                local_dir_use_symlinks=False,
            )
            
            print("Loading model...")
            # Try with flash attention first, fallback to regular attention
            try:
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    attn_implementation="flash_attention_2",
                    torch_dtype=torch.bfloat16,
                    device_map="auto",
                    trust_remote_code=True
                )
                print("✓ Using flash attention")
            except Exception as e:
                print(f"⚠️  Flash attention not available, using regular attention: {e}")
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.bfloat16,
                    device_map="auto",
                    trust_remote_code=True
                )
            
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            print("✓ Dots OCR model loaded successfully!")
            
        except Exception as e:
            print(f"Error loading model: {e}")
            traceback.print_exc()
            raise
    
    @spaces.GPU()
    def inference(self, image: Image.Image, prompt: str, max_new_tokens: int = 24000) -> str:
        """Run inference on an image with the given prompt"""
        try:
            if self.model is None or self.processor is None:
                raise RuntimeError("Model not loaded. Please check model initialization.")
            
            # Prepare messages in the expected format
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image
                        },
                        {"type": "text", "text": prompt}
                    ]
                }
            ]
            
            # Apply chat template
            text = self.processor.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # Process vision information
            image_inputs, video_inputs = process_vision_info(messages)
            
            # Prepare inputs
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            
            # Move to device
            inputs = inputs.to(self.device)
            
            # Generate output
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    do_sample=False,
                    temperature=0.1
                )
            
            # Decode output
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed,
                skip_special_tokens=True,
                clean_up_tokenization_spaces=False
            )
            
            return output_text[0] if output_text else ""
            
        except Exception as e:
            print(f"Error during inference: {e}")
            traceback.print_exc()
            return f"Error during inference: {str(e)}"
    
    def extract_text_simple(self, image_path: str) -> str:
        """Extract text from image with simple OCR prompt"""
        try:
            image = fetch_image(image_path)
            result = self.inference(image, SIMPLE_OCR_PROMPT)
            return result
        except Exception as e:
            return f"Error extracting text: {str(e)}"
    
    def extract_layout_and_text(self, image_path: str) -> dict:
        """Extract detailed layout information and text from image"""
        try:
            image = fetch_image(image_path)
            result = self.inference(image, LAYOUT_PROMPT)
            
            try:
                # Try to parse as JSON
                layout_data = json.loads(result)
                return layout_data
            except json.JSONDecodeError:
                # If JSON parsing fails, return raw text
                return {"raw_output": result}
                
        except Exception as e:
            return {"error": f"Error extracting layout: {str(e)}"}
    
    def process_pdf(self, pdf_path: str) -> List[dict]:
        """Process all pages of a PDF"""
        try:
            images = load_images_from_pdf(pdf_path)
            if not images:
                return [{"error": "Failed to load PDF"}]
            
            results = []
            for i, image in enumerate(images):
                print(f"Processing page {i+1}/{len(images)}...")
                
                # Process with layout extraction
                layout_result = self.inference(image, LAYOUT_PROMPT)
                
                try:
                    layout_data = json.loads(layout_result)
                except json.JSONDecodeError:
                    layout_data = {"raw_output": layout_result}
                
                results.append({
                    "page": i + 1,
                    "layout_data": layout_data,
                    "raw_output": layout_result
                })
            
            return results
            
        except Exception as e:
            return [{"error": f"Error processing PDF: {str(e)}"}]

def create_gradio_interface(app: DotsOCRApp):
    """Create a Gradio interface for the Dots OCR app"""

    def process_file(file_path, extraction_mode):
        """Process uploaded file"""
        if not file_path:
            return "Please upload a file first.", None

        try:
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.pdf':
                results = app.process_pdf(file_path)
                if results and 'error' not in results[0]:
                    # Combine all pages
                    all_text = []
                    for result in results:
                        if 'layout_data' in result:
                            all_text.append(f"=== Page {result['page']} ===\n")
                            if extraction_mode == "Simple Text":
                                # Extract just text from layout data
                                layout_data = result['layout_data']
                                if isinstance(layout_data, list):
                                    for item in layout_data:
                                        if 'text' in item and item['text']:
                                            all_text.append(item['text'])
                                else:
                                    all_text.append(result.get('raw_output', ''))
                            else:
                                all_text.append(json.dumps(result['layout_data'], indent=2, ensure_ascii=False))

                    return "\n\n".join(all_text), results[0]['layout_data'] if results else None
                else:
                    return f"Error processing PDF: {results[0].get('error', 'Unknown error')}", None

            elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                if extraction_mode == "Simple Text":
                    text = app.extract_text_simple(file_path)
                    return text, None
                else:
                    layout_data = app.extract_layout_and_text(file_path)
                    return json.dumps(layout_data, indent=2, ensure_ascii=False), layout_data

            else:
                return f"Unsupported file format: {file_ext}", None

        except Exception as e:
            return f"Error processing file: {str(e)}", None

    # Create Gradio interface
    with gr.Blocks(title="Dots OCR - Standalone App") as demo:
        gr.HTML("""
        <div style="text-align: center;">
            <h1>🔍 Dots OCR - Standalone Application</h1>
            <p>Extract text and layout information from images and PDFs using only Dots OCR</p>
            <p><strong>No other OCR methods are used - strictly Dots OCR only!</strong></p>
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=1):
                file_input = gr.File(
                    label="Upload Image or PDF",
                    file_types=[".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".pdf"],
                    type="filepath"
                )

                extraction_mode = gr.Radio(
                    choices=["Simple Text", "Layout Analysis"],
                    value="Simple Text",
                    label="Extraction Mode"
                )

                process_btn = gr.Button("🚀 Process with Dots OCR", variant="primary")

            with gr.Column(scale=2):
                output_text = gr.Textbox(
                    label="Extracted Content",
                    lines=20,
                    max_lines=30
                )

                output_json = gr.JSON(
                    label="Layout Data (JSON)",
                    visible=False
                )

        def update_visibility(mode):
            if mode == "Layout Analysis":
                return gr.update(visible=True)
            else:
                return gr.update(visible=False)

        extraction_mode.change(
            update_visibility,
            inputs=[extraction_mode],
            outputs=[output_json]
        )

        process_btn.click(
            process_file,
            inputs=[file_input, extraction_mode],
            outputs=[output_text, output_json]
        )

    return demo

def main():
    """Main function to run the Dots OCR application"""
    print("=" * 60)
    print("🔍 DOTS OCR - Standalone Application")
    print("Based on: https://huggingface.co/spaces/MohamedRashad/Dots-OCR")
    print("=" * 60)

    # Initialize the app
    try:
        app = DotsOCRApp()
    except Exception as e:
        print(f"Failed to initialize Dots OCR: {e}")
        return

    # Test with sample image if available
    test_image = "cmd_images/page_1_image_2.png"
    if os.path.exists(test_image):
        print(f"\n📸 Testing with sample image: {test_image}")

        # Simple text extraction
        print("\n1. Simple text extraction...")
        text = app.extract_text_simple(test_image)
        print("✓ Simple text extraction completed")
        print(f"Extracted text preview: {text[:200]}...")

        # Layout extraction
        print("2. Layout extraction...")
        layout_data = app.extract_layout_and_text(test_image)
        print("✓ Layout extraction completed")

        # Save results
        with open("dots_ocr_app_results.txt", "w", encoding="utf-8") as f:
            f.write("=== Dots OCR App Results ===\n\n")
            f.write("Simple Text:\n")
            f.write("-" * 30 + "\n")
            f.write(text + "\n\n")
            f.write("Layout Data:\n")
            f.write("-" * 30 + "\n")
            f.write(json.dumps(layout_data, indent=2, ensure_ascii=False))

        print("✓ Results saved to 'dots_ocr_app_results.txt'")
    else:
        print(f"⚠️  Test image not found: {test_image}")

    print("\n🎉 Dots OCR application is ready!")

    # Ask user if they want to launch Gradio interface
    launch_ui = input("\nWould you like to launch the Gradio web interface? (y/n): ").lower().strip()

    if launch_ui in ['y', 'yes']:
        print("🚀 Launching Gradio interface...")
        demo = create_gradio_interface(app)
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=True
        )
    else:
        print("You can use the DotsOCRApp class programmatically to process your images and PDFs.")

if __name__ == "__main__":
    main()

# 🔍 Dots OCR - Standalone Application

This is a standalone implementation of Dots OCR based on the Hugging Face space: https://huggingface.co/spaces/MohamedRashad/Dots-OCR

**IMPORTANT: This application uses ONLY Dots OCR for text extraction. No other OCR methods are used as fallback.**

## Features

- ✅ **Pure Dots OCR Implementation** - No fallback to other OCR engines
- 📄 **PDF Support** - Process multi-page PDF documents
- 🖼️ **Image Support** - Process various image formats (PNG, JPG, TIFF, etc.)
- 🎯 **Two Extraction Modes**:
  - Simple Text Extraction
  - Detailed Layout Analysis with bounding boxes
- 🌐 **Web Interface** - Gradio-based user interface
- 💻 **Command Line Interface** - For batch processing
- 📊 **JSON Output** - Structured layout data with categories

## Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements_dots.txt
   ```

2. **Run the Application**:
   ```bash
   python run_dots_ocr.py
   ```

## Usage Options

### 1. Interactive Web Interface

Run the main application and choose to launch the Gradio interface:

```bash
python dots_ocr_app.py
```

When prompted, type `y` to launch the web interface at `http://localhost:7860`

### 2. Command Line Interface

For batch processing or automation:

```bash
# Simple text extraction from image
python dots_ocr_cli.py image.png --mode simple --output result.txt

# Layout analysis from PDF
python dots_ocr_cli.py document.pdf --mode layout --format json --output layout.json

# Process image with layout analysis
python dots_ocr_cli.py page.jpg --mode layout --output layout.txt
```

#### CLI Options:
- `--mode`: `simple` (text only) or `layout` (detailed analysis)
- `--format`: `txt` (plain text) or `json` (structured data)
- `--output`: Output file path

### 3. Programmatic Usage

```python
from dots_ocr_app import DotsOCRApp

# Initialize the app
app = DotsOCRApp()

# Simple text extraction
text = app.extract_text_simple("image.png")
print(text)

# Layout analysis
layout_data = app.extract_layout_and_text("image.png")
print(layout_data)

# Process PDF
results = app.process_pdf("document.pdf")
for result in results:
    print(f"Page {result['page']}: {result['layout_data']}")
```

## Supported Formats

### Input Files:
- **Images**: PNG, JPG, JPEG, BMP, TIFF
- **Documents**: PDF (multi-page support)

### Output Formats:
- **Text**: Plain text extraction
- **JSON**: Structured layout data with:
  - Bounding boxes `[x1, y1, x2, y2]`
  - Categories: Caption, Footnote, Formula, List-item, Page-footer, Page-header, Picture, Section-header, Table, Text, Title
  - Formatted text (Markdown, HTML for tables, LaTeX for formulas)

## Layout Categories

The Dots OCR model recognizes these layout elements:

- **Caption**: Image/table captions
- **Footnote**: Page footnotes
- **Formula**: Mathematical formulas (LaTeX format)
- **List-item**: Bulleted/numbered list items
- **Page-footer**: Page footers
- **Page-header**: Page headers
- **Picture**: Images (text field omitted)
- **Section-header**: Section headings
- **Table**: Tables (HTML format)
- **Text**: Regular paragraph text (Markdown format)
- **Title**: Document/section titles

## Model Information

- **Model**: `rednote-hilab/dots.ocr`
- **Type**: Vision-Language Model for Document Understanding
- **Capabilities**: Multilingual text extraction and layout analysis
- **Hardware**: CUDA support (falls back to CPU if unavailable)

## Files Structure

```
├── dots_ocr_app.py          # Main application with Gradio interface
├── dots_ocr_cli.py          # Command line interface
├── run_dots_ocr.py          # Runner script with dependency management
├── requirements_dots.txt    # Python dependencies
├── README_DOTS_OCR.md      # This documentation
└── models/                  # Downloaded model files (auto-created)
    └── dots-ocr-local/
```

## Troubleshooting

### Common Issues:

1. **CUDA/Flash Attention Errors**:
   - The app automatically falls back to regular attention if flash attention is not available
   - Ensure you have compatible CUDA drivers if using GPU

2. **Model Download**:
   - First run downloads ~6GB model files
   - Ensure stable internet connection
   - Model files are cached locally for future use

3. **Memory Issues**:
   - Large PDFs may require significant RAM
   - Process pages individually if needed
   - Consider using CPU mode for very large documents

4. **Dependency Issues**:
   - Run `python run_dots_ocr.py` to auto-install missing dependencies
   - Ensure Python 3.8+ is installed

## Performance Notes

- **First Run**: Downloads model files (~6GB)
- **GPU**: Significantly faster processing with CUDA
- **CPU**: Slower but functional fallback
- **Memory**: ~8GB RAM recommended for large documents

## License

Based on the original Dots OCR implementation. See the model page for licensing details:
https://huggingface.co/rednote-hilab/dots.ocr

## Support

For issues specific to this standalone implementation, check:
1. Ensure all dependencies are installed
2. Verify input file formats are supported
3. Check available system memory
4. Try CPU mode if GPU issues occur

For model-specific issues, refer to the original repository:
https://github.com/rednote-hilab/dots.ocr

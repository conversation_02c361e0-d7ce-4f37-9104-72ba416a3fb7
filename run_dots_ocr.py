#!/usr/bin/env python3
"""
Runner script for Dots OCR Application
This script handles dependency installation and runs the Dots OCR app
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies"""
    print("🔧 Installing dependencies...")
    
    try:
        # Install requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_dots.txt"
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'torch', 'transformers', 'gradio', 'huggingface_hub', 
        'qwen_vl_utils', 'PIL', 'fitz'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'fitz':
                import fitz
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages

def main():
    """Main runner function"""
    print("=" * 60)
    print("🚀 DOTS OCR - Application Runner")
    print("=" * 60)
    
    # Check if dependencies are installed
    deps_ok, missing = check_dependencies()
    
    if not deps_ok:
        print(f"⚠️  Missing dependencies: {missing}")
        install_deps = input("Would you like to install missing dependencies? (y/n): ").lower().strip()
        
        if install_deps in ['y', 'yes']:
            if not install_dependencies():
                print("❌ Failed to install dependencies. Exiting.")
                return
        else:
            print("❌ Cannot run without required dependencies. Exiting.")
            return
    
    # Check if requirements file exists
    if not os.path.exists("requirements_dots.txt"):
        print("❌ requirements_dots.txt not found!")
        return
    
    # Check if main app file exists
    if not os.path.exists("dots_ocr_app.py"):
        print("❌ dots_ocr_app.py not found!")
        return
    
    print("✅ All checks passed. Starting Dots OCR application...")
    
    # Import and run the main application
    try:
        from dots_ocr_app import main as run_app
        run_app()
    except Exception as e:
        print(f"❌ Error running application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

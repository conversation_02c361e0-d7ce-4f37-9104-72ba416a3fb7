#!/usr/bin/env python3
"""
Test Dots OCR in CPU mode to avoid CUDA compatibility issues
"""

import os
import sys
import json
from pathlib import Path

def test_cpu_mode():
    """Test with CPU mode to avoid CUDA issues"""
    print("🔍 Testing Dots OCR in CPU Mode")
    print("=" * 60)
    
    # Check if model exists
    model_path = "dots_ocr/weights/DotsOCR"
    if not os.path.exists(model_path):
        print(f"❌ Model path not found: {model_path}")
        return False
    
    print(f"✅ Found model at: {model_path}")
    
    try:
        print("\n🚀 Testing App Initialization (CPU Mode)...")
        from dots_ocr_app import DotsOCRApp
        
        # Initialize with CPU mode to avoid CUDA issues
        app = DotsOCRApp(model_path=model_path, force_cpu=True)
        print("✅ App initialized successfully in CPU mode!")
        
        # Test with sample image
        sample_images = [
            "cmd_images/page_1_image_1.png",
            "cmd_images/page_1_image_2.png",
            "cmd_images/page_2_image_1.png"
        ]
        
        for img_path in sample_images:
            if os.path.exists(img_path):
                print(f"\n📸 Testing with: {img_path}")
                
                # Simple text extraction
                print("   Extracting text (this may take a moment on CPU)...")
                text = app.extract_text_simple(img_path)
                
                if "Error during inference" not in text:
                    print(f"   ✅ Text extracted successfully ({len(text)} characters)")
                    print(f"   Preview: {text[:150]}...")
                    
                    # Layout analysis
                    print("   Analyzing layout...")
                    layout_data = app.extract_layout_and_text(img_path)
                    
                    if isinstance(layout_data, dict) and 'error' not in layout_data:
                        if isinstance(layout_data, list):
                            print(f"   ✅ Layout analysis complete ({len(layout_data)} elements)")
                        else:
                            print("   ✅ Layout analysis complete")
                    else:
                        print(f"   ⚠️  Layout analysis: {str(layout_data)[:100]}...")
                    
                    # Save results
                    results = {
                        "image": img_path,
                        "text": text,
                        "layout": layout_data,
                        "device": "cpu"
                    }
                    
                    output_file = f"cpu_test_results_{Path(img_path).stem}.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(results, f, indent=2, ensure_ascii=False)
                    print(f"   💾 Results saved to: {output_file}")
                    
                    return True
                else:
                    print(f"   ❌ Text extraction failed: {text[:100]}...")
                    return False
        
        print("⚠️  No sample images found for testing")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("💻 DOTS OCR - CPU Mode Test")
    print("=" * 60)
    print("This test uses CPU mode to avoid CUDA compatibility issues")
    print("Note: CPU processing will be slower but more reliable")
    print()
    
    success = test_cpu_mode()
    
    if success:
        print("\n🎉 SUCCESS! Dots OCR is working in CPU mode!")
        print("\nTo use the application:")
        print("1. For CPU mode: python dots_ocr_app.py (will auto-detect and use CPU)")
        print("2. For web interface: Choose 'y' when prompted")
        print("3. For CLI: python dots_ocr_cli.py <image_file>")
        print("\nNote: CPU mode is slower but avoids GPU compatibility issues")
    else:
        print("\n❌ Test failed. Check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

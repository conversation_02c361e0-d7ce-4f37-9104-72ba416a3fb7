#!/usr/bin/env python3
"""
Test Dots OCR with existing model weights
Uses the model weights from dots_ocr/weights/DotsOCR
"""

import os
import sys
import json
from pathlib import Path

def test_existing_model():
    """Test with existing model weights"""
    print("🔍 Testing Dots OCR with Existing Model Weights")
    print("=" * 60)
    
    # Check if model exists
    model_path = "dots_ocr/weights/DotsOCR"
    if not os.path.exists(model_path):
        print(f"❌ Model path not found: {model_path}")
        return False
    
    print(f"✅ Found model at: {model_path}")
    
    # Check key model files
    required_files = [
        "config.json",
        "tokenizer.json", 
        "model-00001-of-00002.safetensors",
        "model-00002-of-00002.safetensors"
    ]
    
    for file_name in required_files:
        file_path = os.path.join(model_path, file_name)
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"✅ {file_name}: {size_mb:.1f} MB")
        else:
            print(f"❌ Missing: {file_name}")
            return False
    
    # Test app initialization
    try:
        print("\n🚀 Testing App Initialization...")
        from dots_ocr_app import DotsOCRApp
        
        # Initialize with existing model path
        app = DotsOCRApp(model_path=model_path)
        print("✅ App initialized successfully!")
        
        # Test with sample image
        sample_images = [
            "cmd_images/page_1_image_1.png",
            "cmd_images/page_1_image_2.png",
            "cmd_images/page_2_image_1.png"
        ]
        
        for img_path in sample_images:
            if os.path.exists(img_path):
                print(f"\n📸 Testing with: {img_path}")
                
                # Simple text extraction
                print("   Extracting text...")
                text = app.extract_text_simple(img_path)
                print(f"   ✅ Text extracted ({len(text)} characters)")
                print(f"   Preview: {text[:100]}...")
                
                # Layout analysis
                print("   Analyzing layout...")
                layout_data = app.extract_layout_and_text(img_path)
                if isinstance(layout_data, dict) and 'error' not in layout_data:
                    if isinstance(layout_data, list):
                        print(f"   ✅ Layout analysis complete ({len(layout_data)} elements)")
                    else:
                        print("   ✅ Layout analysis complete (raw output)")
                else:
                    print(f"   ⚠️  Layout analysis: {layout_data}")
                
                # Save results
                results = {
                    "image": img_path,
                    "text": text,
                    "layout": layout_data
                }
                
                output_file = f"test_results_{Path(img_path).stem}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
                print(f"   💾 Results saved to: {output_file}")
                
                return True
        
        print("⚠️  No sample images found for testing")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🎯 DOTS OCR - Test with Existing Model")
    print("=" * 60)
    
    success = test_existing_model()
    
    if success:
        print("\n🎉 SUCCESS! Dots OCR is working with your existing model weights!")
        print("\nNext steps:")
        print("1. Run: python dots_ocr_app.py")
        print("2. Choose 'y' to launch web interface")
        print("3. Or use CLI: python dots_ocr_cli.py <image_file>")
    else:
        print("\n❌ Test failed. Check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

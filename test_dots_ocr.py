#!/usr/bin/env python3
"""
Test script for Dots OCR - Quick testing without full model download
This script tests the basic functionality and can work with existing models
"""

import os
import sys
import json
from pathlib import Path

def test_basic_functionality():
    """Test basic imports and functionality"""
    print("🧪 Testing Dots OCR Basic Functionality")
    print("=" * 50)
    
    # Test imports
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA device: {torch.cuda.get_device_name(0)}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        from transformers import AutoModelForCausalLM, AutoProcessor
        print("✅ Transformers imported successfully")
    except ImportError as e:
        print(f"❌ Transformers import failed: {e}")
        return False
    
    try:
        from qwen_vl_utils import process_vision_info
        print("✅ Qwen VL utils imported successfully")
    except ImportError as e:
        print(f"❌ Qwen VL utils import failed: {e}")
        return False
    
    try:
        import gradio as gr
        print("✅ Gradio imported successfully")
    except ImportError as e:
        print(f"❌ Gradio import failed: {e}")
        return False
    
    try:
        import fitz  # PyMuPDF
        print("✅ PyMuPDF imported successfully")
    except ImportError as e:
        print(f"❌ PyMuPDF import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL imported successfully")
    except ImportError as e:
        print(f"❌ PIL import failed: {e}")
        return False
    
    return True

def test_image_processing():
    """Test image processing utilities"""
    print("\n📸 Testing Image Processing")
    print("-" * 30)
    
    try:
        from dots_ocr_app import fetch_image, smart_resize, load_images_from_pdf
        
        # Test smart_resize function
        height, width = smart_resize(1000, 800)
        print(f"✅ Smart resize: 1000x800 -> {height}x{width}")
        
        # Test with sample image if available
        sample_images = [
            "cmd_images/page_1_image_1.png",
            "cmd_images/page_1_image_2.png",
            "cmd_images/page_2_image_1.png"
        ]
        
        for img_path in sample_images:
            if os.path.exists(img_path):
                try:
                    image = fetch_image(img_path)
                    print(f"✅ Loaded image: {img_path} ({image.size})")
                    break
                except Exception as e:
                    print(f"⚠️  Error loading {img_path}: {e}")
        else:
            print("⚠️  No sample images found in cmd_images/")
        
        return True
        
    except Exception as e:
        print(f"❌ Image processing test failed: {e}")
        return False

def test_model_availability():
    """Test if model files are available"""
    print("\n🤖 Testing Model Availability")
    print("-" * 30)
    
    model_path = "./models/dots-ocr-local"
    
    if os.path.exists(model_path):
        print(f"✅ Model directory exists: {model_path}")
        
        # Check for key model files
        key_files = [
            "config.json",
            "tokenizer.json",
            "preprocessor_config.json"
        ]
        
        for file_name in key_files:
            file_path = os.path.join(model_path, file_name)
            if os.path.exists(file_path):
                print(f"✅ Found: {file_name}")
            else:
                print(f"⚠️  Missing: {file_name}")
        
        # Check for model weights
        safetensors_files = list(Path(model_path).glob("*.safetensors"))
        if safetensors_files:
            print(f"✅ Found {len(safetensors_files)} model weight files")
            for f in safetensors_files:
                size_mb = f.stat().st_size / (1024 * 1024)
                print(f"   {f.name}: {size_mb:.1f} MB")
        else:
            print("⚠️  No model weight files found")
            
        return len(safetensors_files) > 0
    else:
        print(f"⚠️  Model directory not found: {model_path}")
        print("   Model will be downloaded on first run")
        return False

def test_app_initialization():
    """Test app initialization without full model loading"""
    print("\n🚀 Testing App Initialization")
    print("-" * 30)
    
    try:
        # Test if we can import the app class
        from dots_ocr_app import DotsOCRApp
        print("✅ DotsOCRApp class imported successfully")
        
        # Don't actually initialize to avoid model download
        print("⚠️  Skipping full initialization to avoid model download")
        print("   Run 'python dots_ocr_app.py' to initialize with model")
        
        return True
        
    except Exception as e:
        print(f"❌ App initialization test failed: {e}")
        return False

def test_cli_interface():
    """Test CLI interface"""
    print("\n💻 Testing CLI Interface")
    print("-" * 30)
    
    try:
        # Test CLI import
        import dots_ocr_cli
        print("✅ CLI module imported successfully")
        
        # Test argument parsing
        import argparse
        print("✅ Argument parsing available")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 DOTS OCR - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Image Processing", test_image_processing),
        ("Model Availability", test_model_availability),
        ("App Initialization", test_app_initialization),
        ("CLI Interface", test_cli_interface)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Dots OCR is ready to use.")
        print("\nNext steps:")
        print("1. Run 'python dots_ocr_app.py' to start the full application")
        print("2. Or use 'python dots_ocr_cli.py <file>' for command line usage")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the errors above.")
        print("\nTroubleshooting:")
        print("1. Ensure all dependencies are installed: pip install -r requirements_dots.txt")
        print("2. Check Python version (3.8+ required)")
        print("3. Verify CUDA installation if using GPU")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

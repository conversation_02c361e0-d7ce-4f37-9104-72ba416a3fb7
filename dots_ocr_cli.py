#!/usr/bin/env python3
"""
Command Line Interface for Dots OCR
Simple CLI to process images and PDFs with Dots OCR only
"""

import argparse
import json
import os
import sys
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(
        description="Dots OCR - Extract text from images and PDFs using only Dots OCR"
    )
    
    parser.add_argument(
        "input_path",
        type=str,
        help="Path to input image or PDF file"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        default="output.txt",
        help="Output file path (default: output.txt)"
    )
    
    parser.add_argument(
        "--mode", "-m",
        choices=["simple", "layout"],
        default="simple",
        help="Extraction mode: 'simple' for text only, 'layout' for detailed layout analysis"
    )
    
    parser.add_argument(
        "--format", "-f",
        choices=["txt", "json"],
        default="txt",
        help="Output format: 'txt' for plain text, 'json' for structured data"
    )
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_path):
        print(f"❌ Input file not found: {args.input_path}")
        sys.exit(1)
    
    # Import the Dots OCR app
    try:
        from dots_ocr_app import DotsOCRApp
    except ImportError as e:
        print(f"❌ Error importing Dots OCR app: {e}")
        print("Make sure all dependencies are installed. Run: python run_dots_ocr.py")
        sys.exit(1)
    
    # Initialize the app
    print("🔧 Initializing Dots OCR...")
    try:
        app = DotsOCRApp()
    except Exception as e:
        print(f"❌ Failed to initialize Dots OCR: {e}")
        sys.exit(1)
    
    # Process the file
    print(f"📄 Processing: {args.input_path}")
    
    try:
        file_ext = Path(args.input_path).suffix.lower()
        
        if file_ext == '.pdf':
            print("📚 Processing PDF...")
            results = app.process_pdf(args.input_path)
            
            if args.format == "json":
                output_data = results
            else:
                # Extract text from all pages
                text_parts = []
                for result in results:
                    if 'error' in result:
                        text_parts.append(f"Error on page {result.get('page', '?')}: {result['error']}")
                    else:
                        text_parts.append(f"=== Page {result['page']} ===")
                        if args.mode == "simple":
                            # Extract text from layout data
                            layout_data = result.get('layout_data', {})
                            if isinstance(layout_data, list):
                                for item in layout_data:
                                    if 'text' in item and item['text']:
                                        text_parts.append(item['text'])
                            else:
                                text_parts.append(result.get('raw_output', ''))
                        else:
                            text_parts.append(json.dumps(result['layout_data'], indent=2, ensure_ascii=False))
                
                output_data = "\n\n".join(text_parts)
        
        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            print("🖼️  Processing image...")
            
            if args.mode == "simple":
                result = app.extract_text_simple(args.input_path)
                output_data = result if args.format == "txt" else {"text": result}
            else:
                result = app.extract_layout_and_text(args.input_path)
                output_data = result if args.format == "json" else json.dumps(result, indent=2, ensure_ascii=False)
        
        else:
            print(f"❌ Unsupported file format: {file_ext}")
            sys.exit(1)
        
        # Save output
        print(f"💾 Saving results to: {args.output}")
        
        with open(args.output, 'w', encoding='utf-8') as f:
            if args.format == "json" and not isinstance(output_data, str):
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            else:
                f.write(str(output_data))
        
        print("✅ Processing completed successfully!")
        print(f"📄 Results saved to: {args.output}")
        
        # Show preview of results
        if isinstance(output_data, str):
            preview = output_data[:500] + "..." if len(output_data) > 500 else output_data
        else:
            preview = json.dumps(output_data, indent=2, ensure_ascii=False)[:500] + "..."
        
        print("\n📋 Preview of extracted content:")
        print("-" * 50)
        print(preview)
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Simple Dots OCR implementation based on your working main.py
This version uses the exact same configuration that works in main.py
"""

import torch
import json
import os
import sys
from transformers import AutoModelForCausalLM, AutoProcessor
from qwen_vl_utils import process_vision_info

class SimpleDotOCR:
    """Simple Dots OCR class based on main.py"""
    
    def __init__(self, model_path="dots_ocr/weights/DotsOCR"):
        self.model_path = model_path
        self.model = None
        self.processor = None
        
        print(f"🔍 Initializing Simple Dots OCR")
        print(f"📁 Model path: {model_path}")
        
        if not os.path.exists(model_path):
            print(f"❌ Model path not found: {model_path}")
            raise FileNotFoundError(f"Model not found at {model_path}")
        
        self._load_model()
    
    def _load_model(self):
        """Load model using exact same config as main.py"""
        print("🔧 Loading model (same config as main.py)...")
        
        # Exact same configuration as main.py
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path,
            torch_dtype=torch.bfloat16,
            device_map="cpu",
            trust_remote_code=True
        )
        
        self.processor = AutoProcessor.from_pretrained(
            self.model_path, 
            trust_remote_code=True
        )
        
        print("✅ Model loaded successfully!")
    
    def extract_text(self, image_path, prompt_type="simple"):
        """Extract text from image"""
        
        if prompt_type == "simple":
            prompt = "Extract all text content from this image and return it as plain text, maintaining the reading order."
        else:
            # Layout analysis prompt (same as main.py)
            prompt = """Please output the layout information from the PDF image, including each layout element's bbox, its category, and the corresponding text content within the bbox.

1. Bbox format: [x1, y1, x2, y2]

2. Layout Categories: The possible categories are ['Caption', 'Footnote', 'Formula', 'List-item', 'Page-footer', 'Page-header', 'Picture', 'Section-header', 'Table', 'Text', 'Title'].

3. Text Extraction & Formatting Rules:
    - Picture: For the 'Picture' category, the text field should be omitted.
    - Formula: Format its text as LaTeX.
    - Table: Format its text as HTML.
    - All Others (Text, Title, etc.): Format their text as Markdown.

4. Constraints:
    - The output text must be the original text from the image, with no translation.
    - All layout elements must be sorted according to human reading order.

5. Final Output: The entire output must be a single JSON object."""
        
        print(f"📸 Processing: {image_path}")
        print(f"🎯 Mode: {prompt_type}")
        
        # Exact same processing as main.py
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": image_path
                    },
                    {"type": "text", "text": prompt}
                ]
            }
        ]
        
        # Preparation for inference (same as main.py)
        text = self.processor.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        
        image_inputs, video_inputs = process_vision_info(messages)
        inputs = self.processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )
        
        inputs = inputs.to("cpu")  # Same as main.py
        
        print("🚀 Running inference...")
        
        # Inference: Generation of the output (same as main.py)
        generated_ids = self.model.generate(**inputs, max_new_tokens=24000)
        generated_ids_trimmed = [
            out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        output_text = self.processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        result = output_text[0] if output_text else ""
        print("✅ Inference completed!")
        
        return result

def main():
    """Test the simple Dots OCR"""
    print("🎯 Simple Dots OCR Test")
    print("=" * 50)
    
    try:
        # Initialize
        ocr = SimpleDotOCR()
        
        # Test with sample images
        test_images = [
            "cmd_images/page_1_image_1.png",
            "cmd_images/page_1_image_2.png"
        ]
        
        for img_path in test_images:
            if os.path.exists(img_path):
                print(f"\n📋 Testing with: {img_path}")
                
                # Simple text extraction
                print("\n1. Simple text extraction:")
                text = ocr.extract_text(img_path, "simple")
                print(f"   Result length: {len(text)} characters")
                print(f"   Preview: {text[:200]}...")
                
                # Layout analysis
                print("\n2. Layout analysis:")
                layout = ocr.extract_text(img_path, "layout")
                print(f"   Result length: {len(layout)} characters")
                
                # Try to parse as JSON
                try:
                    layout_json = json.loads(layout)
                    print(f"   ✅ Valid JSON with {len(layout_json)} elements")
                except:
                    print("   ⚠️  Not valid JSON, raw output")
                
                # Save results
                results = {
                    "image": img_path,
                    "simple_text": text,
                    "layout_analysis": layout
                }
                
                output_file = f"simple_ocr_results_{os.path.basename(img_path)}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
                
                print(f"   💾 Results saved to: {output_file}")
                break
        else:
            print("⚠️  No test images found")
        
        print("\n🎉 Simple Dots OCR test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

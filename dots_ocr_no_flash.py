"""
Dots OCR without Flash Attention
This script patches the transformers library to bypass flash-attn requirements
and allows Dots OCR to work on Windows without flash-attn.
"""

import sys
import os
import importlib.util
from types import ModuleType

# Patch 1: Create a mock flash_attn module
def create_mock_flash_attn():
    """Create a mock flash_attn module to satisfy import requirements."""
    
    # Create mock flash_attn module
    flash_attn = ModuleType('flash_attn')
    
    # Add mock functions
    def mock_flash_attn_func(*args, **kwargs):
        raise NotImplementedError("Flash attention not available")
    
    def mock_flash_attn_varlen_func(*args, **kwargs):
        raise NotImplementedError("Flash attention not available")
    
    flash_attn.flash_attn_func = mock_flash_attn_func
    flash_attn.flash_attn_varlen_func = mock_flash_attn_varlen_func
    
    # Create flash_attn.flash_attn_interface submodule
    flash_attn_interface = ModuleType('flash_attn.flash_attn_interface')
    flash_attn_interface.flash_attn_func = mock_flash_attn_func
    flash_attn_interface.flash_attn_varlen_func = mock_flash_attn_varlen_func
    
    # Add to sys.modules
    sys.modules['flash_attn'] = flash_attn
    sys.modules['flash_attn.flash_attn_interface'] = flash_attn_interface
    
    # Create a proper spec for the module
    spec = importlib.util.spec_from_loader('flash_attn', loader=None)
    flash_attn.__spec__ = spec
    flash_attn_interface.__spec__ = importlib.util.spec_from_loader('flash_attn.flash_attn_interface', loader=None)
    
    print("✓ Mock flash_attn module created")

# Patch 2: Monkey patch transformers to disable flash attention checks
def patch_transformers():
    """Patch transformers to disable flash attention availability checks."""
    
    try:
        import transformers.utils.import_utils as import_utils
        
        # Override the flash attention availability check
        original_is_flash_attn_2_available = import_utils.is_flash_attn_2_available
        
        def mock_is_flash_attn_2_available():
            return False
        
        import_utils.is_flash_attn_2_available = mock_is_flash_attn_2_available
        
        # Also patch the package availability check
        original_is_package_available = import_utils._is_package_available
        
        def mock_is_package_available(pkg_name):
            if pkg_name == "flash_attn":
                return False
            return original_is_package_available(pkg_name)
        
        import_utils._is_package_available = mock_is_package_available
        
        print("✓ Transformers patched to disable flash attention")
        
    except Exception as e:
        print(f"Warning: Could not patch transformers: {e}")

# Apply patches before importing other modules
create_mock_flash_attn()
patch_transformers()

# Now import the required modules
import torch
from transformers import AutoModelForCausalLM, AutoProcessor
from qwen_vl_utils import process_vision_info
from pathlib import Path
import json

class DotsOCRExtractor:
    """
    Dots OCR extractor that works without flash attention.
    """
    
    def __init__(self, model_path: str = "dots_ocr/weights/DotsOCR", device: str = "auto"):
        """
        Initialize the Dots OCR extractor.
        
        Args:
            model_path: Path to the local Dots OCR model
            device: Device to use ("auto", "cuda", "cpu")
        """
        self.model_path = model_path
        self.device = device if device != "auto" else ("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.processor = None
        
        print(f"Initializing Dots OCR on {self.device}")
        self._load_model()
    
    def _load_model(self):
        """Load the Dots OCR model and processor."""
        try:
            print(f"Loading Dots OCR model from {self.model_path}...")
            
            # Load processor first
            self.processor = AutoProcessor.from_pretrained(
                self.model_path, 
                trust_remote_code=True
            )
            print("✓ Processor loaded")
            
            # Load model with explicit attention implementation
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.bfloat16 if self.device == "cuda" else torch.float32,
                device_map=self.device,
                trust_remote_code=True,
                attn_implementation="eager"  # Force eager attention
            )
            print("✓ Model loaded successfully")
            
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Trying alternative loading method...")
            
            try:
                # Alternative: Load with different settings
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.float32,
                    device_map="cpu",  # Force CPU to avoid CUDA issues
                    trust_remote_code=True,
                    low_cpu_mem_usage=True
                )
                self.device = "cpu"
                print("✓ Model loaded on CPU")
                
            except Exception as e2:
                print(f"Failed to load model: {e2}")
                raise
    
    def extract_text_simple(self, image_path: str) -> str:
        """
        Extract text from image with simple OCR prompt.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Extracted text as string
        """
        prompt = "Please extract all text from this image and return it as plain text, maintaining the reading order."
        return self._process_image(image_path, prompt)
    
    def extract_layout_and_text(self, image_path: str) -> dict:
        """
        Extract detailed layout information and text from image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary containing layout information and extracted text
        """
        prompt = """Please output the layout information from the PDF image, including each layout element's bbox, its category, and the corresponding text content within the bbox.

1. Bbox format: [x1, y1, x2, y2]

2. Layout Categories: The possible categories are ['Caption', 'Footnote', 'Formula', 'List-item', 'Page-footer', 'Page-header', 'Picture', 'Section-header', 'Table', 'Text', 'Title'].

3. Text Extraction & Formatting Rules:
    - Picture: For the 'Picture' category, the text field should be omitted.
    - Formula: Format its text as LaTeX.
    - Table: Format its text as HTML.
    - All Others (Text, Title, etc.): Format their text as Markdown.

4. Constraints:
    - The output text must be the original text from the image, with no translation.
    - All layout elements must be sorted according to human reading order.

5. Final Output: The entire output must be a single JSON object."""
        
        result = self._process_image(image_path, prompt)
        
        try:
            # Try to parse as JSON
            layout_data = json.loads(result)
            return layout_data
        except json.JSONDecodeError:
            # If JSON parsing fails, return raw text
            return {"raw_output": result}
    
    def _process_image(self, image_path: str, prompt: str) -> str:
        """
        Process image with the given prompt.
        
        Args:
            image_path: Path to the image file
            prompt: Prompt for the model
            
        Returns:
            Model output as string
        """
        if self.model is None or self.processor is None:
            raise RuntimeError("Model not loaded properly")
        
        print(f"Processing image: {image_path}")
        
        # Prepare messages
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": image_path
                    },
                    {"type": "text", "text": prompt}
                ]
            }
        ]
        
        # Preparation for inference
        text = self.processor.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )
        
        image_inputs, video_inputs = process_vision_info(messages)
        inputs = self.processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )
        
        inputs = inputs.to(self.device)
        
        # Inference: Generation of the output
        print("Generating response...")
        with torch.no_grad():
            generated_ids = self.model.generate(
                **inputs, 
                max_new_tokens=24000,
                do_sample=False,
                temperature=0.0,
                pad_token_id=self.processor.tokenizer.eos_token_id
            )
        
        generated_ids_trimmed = [
            out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        
        output_text = self.processor.batch_decode(
            generated_ids_trimmed, 
            skip_special_tokens=True, 
            clean_up_tokenization_spaces=False
        )
        
        return output_text[0] if output_text else ""

def main():
    """
    Test the Dots OCR extractor.
    """
    print("=== Dots OCR Test (No Flash Attention) ===\n")
    
    # Check if model exists
    model_path = "dots_ocr/weights/DotsOCR"
    if not Path(model_path).exists():
        print(f"Error: Model not found at {model_path}")
        print("Please make sure you have downloaded the Dots OCR model.")
        return
    
    # Check if test image exists
    image_path = "cmd_images/page_1_image_2.png"
    if not Path(image_path).exists():
        print(f"Error: Test image not found at {image_path}")
        print("Please provide a valid image path.")
        return
    
    try:
        # Initialize extractor
        extractor = DotsOCRExtractor(model_path)
        
        # Test simple text extraction
        print("1. Testing simple text extraction...")
        text = extractor.extract_text_simple(image_path)
        print("✓ Simple text extraction completed")
        print(f"Extracted text:\n{text}\n")
        
        # Test layout extraction
        print("2. Testing layout extraction...")
        layout_data = extractor.extract_layout_and_text(image_path)
        print("✓ Layout extraction completed")
        
        # Save results
        with open("dots_ocr_results.txt", "w", encoding="utf-8") as f:
            f.write("=== Dots OCR Results ===\n\n")
            f.write("Simple Text:\n")
            f.write("-" * 30 + "\n")
            f.write(text + "\n\n")
            f.write("Layout Data:\n")
            f.write("-" * 30 + "\n")
            f.write(json.dumps(layout_data, indent=2))
        
        print("✓ Results saved to dots_ocr_results.txt")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

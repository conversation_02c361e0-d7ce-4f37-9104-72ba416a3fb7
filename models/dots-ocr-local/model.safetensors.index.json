{"metadata": {"total_size": 6078358528}, "weight_map": {"lm_head.weight": "model-00001-of-00002.safetensors", "model.embed_tokens.weight": "model-00001-of-00002.safetensors", "model.layers.0.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.0.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.0.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.1.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.1.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.10.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.10.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.11.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.11.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.12.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.12.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.13.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.13.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.14.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.14.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.14.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.14.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.14.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.14.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.15.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.15.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.15.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.15.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.15.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.15.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.15.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.15.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.15.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.15.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.15.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.15.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.16.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.16.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.16.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.16.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.16.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.16.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.16.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.16.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.16.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.16.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.16.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.16.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.17.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.17.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.17.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.17.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.17.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.17.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.17.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.17.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.17.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.17.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.17.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.17.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.18.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.18.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.18.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.18.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.18.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.18.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.18.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.18.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.18.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.18.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.18.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.18.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.19.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.19.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.19.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.19.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.19.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.19.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.19.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.19.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.19.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.19.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.19.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.19.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.2.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.2.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.20.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.20.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.20.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.20.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.20.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.20.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.21.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.21.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.21.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.21.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.21.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.21.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.21.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.21.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.21.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.21.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.21.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.21.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.22.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.22.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.22.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.22.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.22.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.22.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.22.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.22.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.22.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.22.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.22.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.22.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.23.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.23.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.23.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.23.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.23.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.23.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.23.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.23.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.23.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.23.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.23.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.23.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.24.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.24.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.24.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.24.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.24.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.24.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.24.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.24.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.24.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.24.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.24.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.24.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.25.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.25.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.25.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.25.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.25.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.25.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.25.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.25.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.25.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.25.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.25.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.25.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.26.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.26.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.26.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.26.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.26.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.26.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.26.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.26.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.26.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.26.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.26.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.26.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.27.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.27.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.27.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.27.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.27.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.27.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.27.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.27.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.27.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.27.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.27.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.27.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.3.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.3.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.4.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.4.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.5.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.5.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.6.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.6.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.7.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.7.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.8.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.8.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.input_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.9.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "model.layers.9.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "model.norm.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.0.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.0.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.0.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.0.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.0.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.0.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.0.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.1.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.1.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.1.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.1.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.1.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.1.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.1.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.10.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.10.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.10.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.10.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.10.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.10.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.10.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.11.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.11.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.11.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.11.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.11.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.11.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.11.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.12.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.12.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.12.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.12.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.12.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.12.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.12.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.13.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.13.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.13.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.13.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.13.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.13.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.13.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.14.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.14.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.14.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.14.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.14.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.14.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.14.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.15.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.15.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.15.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.15.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.15.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.15.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.15.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.16.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.16.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.16.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.16.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.16.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.16.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.16.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.17.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.17.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.17.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.17.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.17.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.17.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.17.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.18.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.18.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.18.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.18.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.18.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.18.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.18.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.19.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.19.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.19.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.19.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.19.mlp.fc3.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.19.norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.19.norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.2.attn.proj.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.2.attn.qkv.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.2.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.2.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.blocks.2.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.2.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.2.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.20.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.20.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.20.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.20.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.20.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.20.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.20.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.21.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.21.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.21.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.21.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.21.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.21.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.21.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.22.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.22.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.22.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.22.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.22.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.22.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.22.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.23.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.23.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.23.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.23.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.23.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.23.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.23.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.24.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.24.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.24.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.24.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.24.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.24.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.24.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.25.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.25.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.25.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.25.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.25.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.25.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.25.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.26.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.26.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.26.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.26.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.26.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.26.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.26.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.27.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.27.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.27.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.27.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.27.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.27.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.27.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.28.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.28.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.28.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.28.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.28.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.28.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.28.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.29.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.29.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.29.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.29.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.29.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.29.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.29.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.3.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.3.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.3.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.3.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.3.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.3.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.3.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.30.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.30.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.30.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.30.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.30.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.30.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.30.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.31.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.31.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.31.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.31.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.31.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.31.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.31.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.32.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.32.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.32.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.32.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.32.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.32.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.32.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.33.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.33.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.33.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.33.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.33.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.33.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.33.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.34.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.34.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.34.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.34.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.34.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.34.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.34.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.35.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.35.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.35.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.35.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.35.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.35.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.35.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.36.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.36.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.36.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.36.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.36.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.36.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.36.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.37.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.37.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.37.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.37.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.37.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.37.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.37.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.38.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.38.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.38.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.38.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.38.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.38.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.38.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.39.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.39.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.39.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.39.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.39.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.39.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.39.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.4.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.4.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.4.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.4.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.4.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.4.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.4.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.40.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.40.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.40.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.40.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.40.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.40.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.40.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.41.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.41.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.41.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.41.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.41.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.41.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.41.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.5.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.5.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.5.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.5.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.5.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.5.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.5.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.6.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.6.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.6.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.6.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.6.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.6.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.6.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.7.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.7.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.7.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.7.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.7.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.7.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.7.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.8.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.8.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.8.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.8.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.8.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.8.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.8.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.9.attn.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.9.attn.qkv.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.9.mlp.fc1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.9.mlp.fc2.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.9.mlp.fc3.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.9.norm1.weight": "model-00002-of-00002.safetensors", "vision_tower.blocks.9.norm2.weight": "model-00002-of-00002.safetensors", "vision_tower.merger.ln_q.bias": "model-00002-of-00002.safetensors", "vision_tower.merger.ln_q.weight": "model-00002-of-00002.safetensors", "vision_tower.merger.mlp.0.bias": "model-00002-of-00002.safetensors", "vision_tower.merger.mlp.0.weight": "model-00002-of-00002.safetensors", "vision_tower.merger.mlp.2.bias": "model-00002-of-00002.safetensors", "vision_tower.merger.mlp.2.weight": "model-00002-of-00002.safetensors", "vision_tower.patch_embed.patchifier.norm.weight": "model-00002-of-00002.safetensors", "vision_tower.patch_embed.patchifier.proj.bias": "model-00002-of-00002.safetensors", "vision_tower.patch_embed.patchifier.proj.weight": "model-00002-of-00002.safetensors", "vision_tower.post_trunk_norm.weight": "model-00002-of-00002.safetensors"}}
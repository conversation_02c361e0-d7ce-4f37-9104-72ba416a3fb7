{"add_bos_token": false, "add_prefix_space": false, "added_tokens_decoder": {"151643": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151644": {"content": "<|im_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151645": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151646": {"content": "<|object_ref_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151647": {"content": "<|object_ref_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151648": {"content": "<|box_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151649": {"content": "<|box_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151650": {"content": "<|quad_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151651": {"content": "<|quad_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151652": {"content": "<|vision_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151653": {"content": "<|vision_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151654": {"content": "<|vision_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151655": {"content": "<|image_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151656": {"content": "<|video_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151657": {"content": "<tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151658": {"content": "</tool_call>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151659": {"content": "<|fim_prefix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151660": {"content": "<|fim_middle|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151661": {"content": "<|fim_suffix|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151662": {"content": "<|fim_pad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151663": {"content": "<|repo_name|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151664": {"content": "<|file_sep|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}, "151665": {"content": "<|imgpad|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151666": {"content": "<|img|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151667": {"content": "<|endofimg|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151668": {"content": "<|systemprompt|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151669": {"content": "<|endofsystemprompt|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151670": {"content": "<|user|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151671": {"content": "<|endofuser|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151672": {"content": "<|assistant|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151673": {"content": "<|endofassistant|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151674": {"content": "<|ref_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151675": {"content": "<|ref_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151676": {"content": "[SEP]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151677": {"content": "<|pic|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151678": {"content": "<|text|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151679": {"content": "<|pictotext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151680": {"content": "[PAD]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151681": {"content": "<|slice|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151682": {"content": "<|endofslice|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151683": {"content": "<|imgrowend|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151684": {"content": "<|polygon_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151685": {"content": "<|polygon_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151686": {"content": "<|image_gen_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "151687": {"content": "<|image_gen_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["<|im_start|>", "<|im_end|>", "<|object_ref_start|>", "<|object_ref_end|>", "<|box_start|>", "<|box_end|>", "<|quad_start|>", "<|quad_end|>", "<|vision_start|>", "<|vision_end|>", "<|vision_pad|>", "<|image_pad|>", "<|video_pad|>"], "bos_token": null, "chat_template": "{%- for m in messages %}\n    {%- if m.role == 'system' %}\n        {{- '<|system|>' + m.content + '<|endofsystem|>\\n' }}\n    {%- elif m.role == 'user' %}\n        {{- '<|user|>' + m.content + '<|endofuser|>' }}\n    {%- elif m.role == 'assistant' %}\n        {{- '<|assistant|>' + m.content }}\n        {%- if not loop.last %}\n            {{- '<|endofassistant|>' }}\n        {%- endif %}\n    {%- endif %}\n{%- endfor %}\n{%- if messages[-1].role != 'assistant' %}\n    {{- '<|assistant|>' }}\n{%- endif %}", "clean_up_tokenization_spaces": false, "eos_token": "<|endoftext|>", "errors": "replace", "model_max_length": 131072, "pad_token": "[PAD]", "split_special_tokens": false, "tokenizer_class": "Qwen2Tokenizer", "unk_token": null}